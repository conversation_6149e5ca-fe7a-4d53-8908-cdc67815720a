package com.ifallious.utils.render;

import com.ifallious.event.GlobalEventBus;
import com.ifallious.event.Render3DEvent;
import com.ifallious.utils.config.ConfigManager;
import gg.essential.universal.UChat;
import meteordevelopment.meteorclient.renderer.ShapeMode;
import meteordevelopment.meteorclient.utils.render.color.Color;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.util.math.BlockPos;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

public class BlockRenderer {
    // Inner class to store block rendering information with animation support
    public static class BlockRenderInfo {
        public final BlockPos pos;
        public final Color originalSideColor;
        public final Color originalLineColor;
        public final ShapeMode shapeMode;
        public final int excludedDirections;

        // Animation properties
        public float currentOpacity = 1.0f;
        public boolean isRemoving = false;
        public long removalStartTime = 0;
        public long addTime = System.currentTimeMillis();

        public BlockRenderInfo(BlockPos pos, Color sideColor, Color lineColor, ShapeMode shapeMode, int excludedDirections) {
            this.pos = pos;
            this.originalSideColor = sideColor;
            this.originalLineColor = lineColor;
            this.shapeMode = shapeMode;
            this.excludedDirections = excludedDirections;
        }

        public BlockRenderInfo(BlockPos pos, Color sideColor, Color lineColor, ShapeMode shapeMode) {
            this(pos, sideColor, lineColor, shapeMode, 0);
        }

        // Get current colors with opacity applied
        public Color getCurrentSideColor() {
            return new Color(
                originalSideColor.r,
                originalSideColor.g,
                originalSideColor.b,
                (int)(originalSideColor.a * currentOpacity)
            );
        }

        public Color getCurrentLineColor() {
            return new Color(
                originalLineColor.r,
                originalLineColor.g,
                originalLineColor.b,
                (int)(originalLineColor.a * currentOpacity)
            );
        }
    }

    // Store the blocks with their rendering parameters
    private static final List<BlockRenderInfo> blocksToRender = new ArrayList<>();

    // Animation settings
    private static final long FADE_DURATION_MS = 300; // 300ms fade duration

    public BlockRenderer() {
        GlobalEventBus.subscribe(this);
    }

    // Toggle smoothing on/off
    public static void setSmoothingEnabled(boolean enabled) {
        ConfigManager.setFeature("blockSmoothing", enabled);
        if (!enabled) {
            // If smoothing is disabled, reset all opacities and clear removing blocks
            synchronized (blocksToRender) {
                Iterator<BlockRenderInfo> iterator = blocksToRender.iterator();
                while (iterator.hasNext()) {
                    BlockRenderInfo info = iterator.next();
                    if (info.isRemoving) {
                        iterator.remove();
                    } else {
                        info.currentOpacity = 1.0f;
                    }
                }
            }
        }
    }

    public static boolean isSmoothingEnabled() {
        return ConfigManager.getFeature("blockSmoothing");
    }

    // Method to add a block with default rendering parameters (red color, both shape mode)
    public static void addBlock(int x, int y, int z) {
        addBlock(x, y, z, new Color(255, 0, 0, 50), new Color(255, 0, 0, 255), ShapeMode.Both);
    }

    /**
     *
     * @param x
     * @param y
     * @param z
     * @param sideColor
     * @param lineColor
     * @param shapeMode
     */
    public static void addBlock(int x, int y, int z, Color sideColor, Color lineColor, ShapeMode shapeMode) {
        addBlock(x, y, z, sideColor, lineColor, shapeMode, 0);
    }

    // Method to add a block with full customization
    public static void addBlock(int x, int y, int z, Color sideColor, Color lineColor, ShapeMode shapeMode, int excludedDirections) {
        BlockPos pos = new BlockPos(x, y, z);
        BlockRenderInfo info = new BlockRenderInfo(pos, sideColor, lineColor, shapeMode, excludedDirections);
        blocksToRender.add(info);
    }

    // Method to add a block using BlockRenderInfo directly
    public static void addBlock(BlockRenderInfo blockInfo) {
        blocksToRender.add(blockInfo);
    }

    // Method to remove a block by position with smooth transition
    public static void removeBlock(int x, int y, int z) {
        BlockPos targetPos = new BlockPos(x, y, z);
        if (isSmoothingEnabled()) {
            synchronized (blocksToRender) {
                for (BlockRenderInfo info : blocksToRender) {
                    if (info.pos.equals(targetPos) && !info.isRemoving) {
                        info.isRemoving = true;
                        info.removalStartTime = System.currentTimeMillis();
                        return;
                    }
                }
            }
        } else {
            blocksToRender.removeIf(info -> info.pos.equals(targetPos));
        }
    }

    // Method to remove a specific BlockRenderInfo with smooth transition
    public static void removeBlock(BlockRenderInfo blockInfo) {
        if (isSmoothingEnabled() && !blockInfo.isRemoving) {
            blockInfo.isRemoving = true;
            blockInfo.removalStartTime = System.currentTimeMillis();
        } else {
            blocksToRender.remove(blockInfo);
        }
    }

    // Method to clear all blocks with smooth transition
    public static void clearBlocks() {
        if (isSmoothingEnabled()) {
            synchronized (blocksToRender) {
                long currentTime = System.currentTimeMillis();
                for (BlockRenderInfo info : blocksToRender) {
                    if (!info.isRemoving) {
                        info.isRemoving = true;
                        info.removalStartTime = currentTime;
                    }
                }
            }
        } else {
            blocksToRender.clear();
        }
    }

    // Method to immediately clear all blocks (for when you really want them gone)
    public static void clearBlocksImmediate() {
        blocksToRender.clear();
    }

    // Method to get all blocks (useful for inspection/debugging)
    public static List<BlockRenderInfo> getBlocks() {
        return new ArrayList<>(blocksToRender);
    }

    // Method to update a block's rendering parameters
    public static boolean updateBlock(int x, int y, int z, Color sideColor, Color lineColor, ShapeMode shapeMode) {
        return updateBlock(x, y, z, sideColor, lineColor, shapeMode, 0);
    }

    // Method to update a block's rendering parameters with excluded directions
    public static boolean updateBlock(int x, int y, int z, Color sideColor, Color lineColor, ShapeMode shapeMode, int excludedDirections) {
        BlockPos targetPos = new BlockPos(x, y, z);
        for (int i = 0; i < blocksToRender.size(); i++) {
            BlockRenderInfo info = blocksToRender.get(i);
            if (info.pos.equals(targetPos)) {
                blocksToRender.set(i, new BlockRenderInfo(targetPos, sideColor, lineColor, shapeMode, excludedDirections));
                return true;
            }
        }
        return false; // Block not found
    }
    
    @EventHandler
    private void onRender(Render3DEvent event) {
        if (isSmoothingEnabled()) {
            updateAnimations();
        }

        // Render all blocks with their current opacity
        synchronized (blocksToRender) {
            for (BlockRenderInfo info : blocksToRender) {
                // Skip blocks that are fully faded out
                if (info.currentOpacity <= 0.0f) continue;

                // Draw a box at the block position using the block's current colors
                event.renderer.box(
                    info.pos.getX(), info.pos.getY(), info.pos.getZ(),           // Start coordinates
                    info.pos.getX() + 1, info.pos.getY() + 1, info.pos.getZ() + 1, // End coordinates
                    info.getCurrentSideColor(),                                   // Current side color with opacity
                    info.getCurrentLineColor(),                                   // Current line color with opacity
                    info.shapeMode,                                              // Custom shape mode
                    info.excludedDirections                                      // Custom excluded directions
                );
            }
        }
    }

    // Update animations for smooth transitions
    private void updateAnimations() {
        synchronized (blocksToRender) {
            Iterator<BlockRenderInfo> iterator = blocksToRender.iterator();
            long currentTime = System.currentTimeMillis();

            while (iterator.hasNext()) {
                BlockRenderInfo info = iterator.next();

                if (info.isRemoving) {
                    // Calculate fade-out progress
                    long elapsed = currentTime - info.removalStartTime;
                    float progress = Math.min(1.0f, (float) elapsed / FADE_DURATION_MS);
                    info.currentOpacity = 1.0f - progress;

                    // Remove block when fully faded
                    if (progress >= 1.0f) {
                        iterator.remove();
                    }
                } else {
                    // Fade-in for new blocks
                    long elapsed = currentTime - info.addTime;
                    float progress = Math.min(1.0f, (float) elapsed / FADE_DURATION_MS);
                    info.currentOpacity = progress;
                }
            }
        }
    }
}