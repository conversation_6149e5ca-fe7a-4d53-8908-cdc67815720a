package com.ifallious.utils;

import com.ifallious.Wynnutils;
import gg.essential.universal.UMinecraft;
import net.minecraft.client.MinecraftClient;
import net.minecraft.network.packet.c2s.play.PlayerInteractItemC2SPacket;
import net.minecraft.network.packet.s2c.play.GameMessageS2CPacket;
import net.minecraft.text.Text;
import net.minecraft.util.Hand;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.hit.HitResult;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.RaycastContext;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.util.Objects;

/**
 * General utility methods for the mod
 */
public class Utils {
    public static double frameTime;

    private static final MinecraftClient client = MinecraftClient.getInstance();

    public static BlockPos getTargetedBlockPos() {
        if (client.player == null || client.world == null) {
            return null;
        }

        // Get player's eye position and look direction
        Vec3d eyePos = client.player.getEyePos();
        Vec3d lookDirection = client.player.getRotationVec(1.0f);

        // Set a very large distance for "unlimited" range (1000 blocks should be sufficient)
        double maxDistance = 1000.0;
        Vec3d endPos = eyePos.add(lookDirection.multiply(maxDistance));

        // Perform raycast from eye position to end position
        RaycastContext raycastContext = new RaycastContext(
            eyePos,
            endPos,
            RaycastContext.ShapeType.OUTLINE,  // Only hit solid blocks
            RaycastContext.FluidHandling.NONE, // Ignore fluids
            client.player
        );

        BlockHitResult hitResult = client.world.raycast(raycastContext);

        // Check if we hit a block
        if (hitResult.getType() == HitResult.Type.BLOCK) {
            return hitResult.getBlockPos();
        }

        return null; // No block hit
    }

    /**
     * Calculate the time in seconds since a timestamp
     * @param start The starting timestamp in milliseconds
     * @return The number of seconds that have passed
     */
    public static long getTimeSince(long start) {
        return (System.currentTimeMillis() - start) / 1000;
    }
    /**
     * Find the hotbar slot containing an item with the specified name
     * @param itemName The exact name of the item to find
     * @return The slot index (0-7) or -1 if not found
     */
    public static int getSlotInHotbar(String itemName) {
        try {
            if (Wynnutils.mc.player == null || itemName == null) {
                return -1;
            }
            for (int i = 0; i < 8; i++) {
                try {
                    Text text = Wynnutils.mc.player.getInventory().getStack(i).getCustomName();
                    if (text == null) continue;
                    if (text.getString().equals(itemName)) {
                        return i;
                    }
                } catch (Exception e) {
                    ErrorReporter.reportError("Utils getSlot item check failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
                }
            }
            return -1;
        } catch (Exception e) {
            ErrorReporter.reportError("Utils getSlot failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
            return -1;
        }
    }
    public static int getSlotInInventory(String itemName) {
        try {
            if (Wynnutils.mc.player == null || itemName == null) {
                return -1;
            }
            for (int i = 0; i < UMinecraft.getPlayer().getInventory().size(); i++) {
                try {
                    Text text = Wynnutils.mc.player.getInventory().getStack(i).getCustomName();
                    if (text == null) continue;
                    if (text.getString().contains(itemName)) {
                        return i;
                    }
                } catch (Exception e) {
                    ErrorReporter.reportError("Utils getSlot item check failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
                }
            }
            return -1;
        } catch (Exception e) {
            ErrorReporter.reportError("Utils getSlot failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
            return -1;
        }
    }

    public static void useItemInSlot(int slot) {
        MinecraftClient client = MinecraftClient.getInstance();

        if (client.player == null || client.getNetworkHandler() == null) return;

        // Save current slot
        int prevSlot = client.player.getInventory().selectedSlot;

        // Temporarily spoof slot
        client.player.getInventory().selectedSlot = slot;

        // Send the packet to the server
        client.getNetworkHandler().sendPacket(
                new PlayerInteractItemC2SPacket(Hand.MAIN_HAND, 0, client.player.getYaw(1.0f), client.player.getPitch(1.0f))
        );

        // Restore slot immediately
        client.player.getInventory().selectedSlot = prevSlot;
    }

    /**
     * Display a title on the client's screen
     * @param titleText The text to display as a title
     */
    public static void displayTitle(String titleText) {
        try {
            if (titleText == null) {
                return;
            }
            MinecraftClient client = Wynnutils.mc;
            if (client != null && client.inGameHud != null) {
                client.inGameHud.setTitle(Text.of(titleText));
                client.inGameHud.setTitleTicks(0, 20, 5);
            }
        } catch (Exception e) {
            ErrorReporter.reportError("Utils displayTitle failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
        }
    }

    /**
     * Simulate a chat message with the given text
     * @param text The text to send as a chat message
     */
    public static void simulateChat(String text) {
        try {
            if (text == null) {
                return;
            }
            Objects.requireNonNull(UMinecraft.getNetHandler()).onGameMessage(new GameMessageS2CPacket(Text.of(text), false));
        } catch (Exception e) {
            Wynnutils.LOGGER.error("Utils simulateChat failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
        }
    }

    /**
     * Executes a Command as the Player
     * @param command The command to send (without /)
     */
    public static void sendCommand(String command) {
        Objects.requireNonNull(UMinecraft.getNetHandler()).sendChatCommand(command);
    }

    public static float horizontalDistance(float x1, float z1, float x2, float z2) {
        return (float) Math.sqrt((x2 - x1) * (x2 - x1) + (z2 - z1) * (z2 - z1));
    }
    
    public static float distance3D(float x1, float y1, float z1, float x2, float y2, float z2) {
        return (float) Math.sqrt((x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1) + (z2 - z1) * (z2 - z1));
    }
}

