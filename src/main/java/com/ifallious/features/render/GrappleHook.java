package com.ifallious.features.render;

import com.ifallious.event.GlobalEventBus;
import com.ifallious.event.Render3DEvent;
import com.ifallious.event.TickEvent;
import com.ifallious.utils.Utils;
import com.ifallious.utils.render.BlockRenderer;
import gg.essential.universal.UChat;
import gg.essential.universal.UMinecraft;
import gg.essential.universal.wrappers.UPlayer;
import meteordevelopment.meteorclient.renderer.ShapeMode;
import meteordevelopment.meteorclient.utils.render.color.Color;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.util.math.BlockPos;

public class GrappleHook {
    private BlockPos lastTarget = null;

    public GrappleHook() {
        GlobalEventBus.subscribe(this);
    }

    @EventHandler
    public void onFrame(Render3DEvent e) {
        BlockPos target = Utils.getTargetedBlockPos();

        // Only update if target changed
        if (target == null) {
            if (lastTarget != null) {
                BlockRenderer.clearBlocks();
                lastTarget = null;
            }
            return;
        }

        // Check if target position changed
        if (lastTarget == null || !lastTarget.equals(target)) {
            BlockRenderer.clearBlocks();
            float distance = Utils.distance3D(target.getX(), target.getY(), target.getZ(), (float) UPlayer.getPosX(), (float) UPlayer.getPosY(), (float) UPlayer.getPosZ());
            BlockRenderer.addBlock(target.getX(), target.getY(), target.getZ(),
                distance < 30 ? new Color(0, 255, 0, 30) : new Color(255, 0, 0, 30),
                distance < 30 ? new Color(0, 255, 0, 100) : new Color(255, 0, 0, 100),
                ShapeMode.Both);
            lastTarget = target;
        }
    }
}
